<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="10ff3e817f9d49c8a8a01a6c4e87f323" class="chart-container" style="width:1000px; height:700px; "></div>
    <script>
        var chart_10ff3e817f9d49c8a8a01a6c4e87f323 = echarts.init(
            document.getElementById('10ff3e817f9d49c8a8a01a6c4e87f323'), 'white', {renderer: 'canvas'});
        var option_10ff3e817f9d49c8a8a01a6c4e87f323 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    67.15,
                    65.97,
                    65.81,
                    68.0
                ],
                [
                    65.4,
                    65.18,
                    65.01,
                    65.95
                ],
                [
                    65.21,
                    66.22,
                    64.87,
                    66.49
                ],
                [
                    66.0,
                    65.18,
                    64.76,
                    66.18
                ],
                [
                    67.88,
                    65.75,
                    65.6,
                    67.88
                ],
                [
                    65.67,
                    65.9,
                    65.5,
                    66.45
                ],
                [
                    65.89,
                    66.81,
                    65.38,
                    66.85
                ],
                [
                    66.38,
                    65.36,
                    65.22,
                    66.65
                ],
                [
                    65.36,
                    65.36,
                    65.01,
                    67.55
                ],
                [
                    65.01,
                    64.75,
                    64.44,
                    65.8
                ],
                [
                    65.0,
                    66.03,
                    64.2,
                    66.37
                ],
                [
                    65.11,
                    68.19,
                    65.0,
                    68.55
                ],
                [
                    68.05,
                    68.89,
                    67.2,
                    70.55
                ],
                [
                    68.74,
                    67.74,
                    67.3,
                    70.88
                ],
                [
                    67.8,
                    67.98,
                    66.18,
                    68.25
                ],
                [
                    67.95,
                    67.2,
                    66.88,
                    68.63
                ],
                [
                    67.0,
                    68.99,
                    66.66,
                    69.17
                ],
                [
                    69.07,
                    68.12,
                    67.89,
                    69.98
                ],
                [
                    67.9,
                    67.31,
                    67.04,
                    68.55
                ],
                [
                    67.3,
                    64.37,
                    64.16,
                    67.42
                ],
                [
                    64.04,
                    60.81,
                    60.1,
                    64.13
                ],
                [
                    60.81,
                    59.43,
                    59.3,
                    61.18
                ],
                [
                    59.43,
                    58.9,
                    58.59,
                    59.95
                ],
                [
                    59.17,
                    60.36,
                    58.91,
                    60.52
                ],
                [
                    60.0,
                    59.4,
                    57.36,
                    60.1
                ],
                [
                    59.2,
                    60.08,
                    58.98,
                    60.86
                ],
                [
                    59.99,
                    58.61,
                    58.55,
                    61.0
                ],
                [
                    58.0,
                    57.95,
                    57.48,
                    58.95
                ],
                [
                    57.92,
                    59.87,
                    56.5,
                    59.95
                ],
                [
                    59.87,
                    58.95,
                    58.81,
                    60.15
                ],
                [
                    59.4,
                    59.22,
                    58.55,
                    60.47
                ],
                [
                    58.81,
                    60.86,
                    58.72,
                    61.52
                ],
                [
                    61.07,
                    60.17,
                    60.11,
                    61.38
                ],
                [
                    60.5,
                    60.41,
                    59.6,
                    60.6
                ],
                [
                    60.0,
                    59.68,
                    59.5,
                    60.45
                ],
                [
                    60.41,
                    59.22,
                    59.2,
                    61.26
                ],
                [
                    59.21,
                    59.82,
                    59.0,
                    59.88
                ],
                [
                    60.2,
                    59.12,
                    59.09,
                    60.38
                ],
                [
                    60.01,
                    60.8,
                    59.6,
                    61.18
                ],
                [
                    60.4,
                    62.16,
                    60.24,
                    62.21
                ],
                [
                    62.18,
                    63.27,
                    61.67,
                    64.64
                ],
                [
                    63.63,
                    63.99,
                    63.22,
                    64.5
                ],
                [
                    63.7,
                    63.83,
                    63.03,
                    64.99
                ],
                [
                    63.6,
                    65.63,
                    63.23,
                    65.88
                ],
                [
                    65.27,
                    63.68,
                    63.63,
                    65.48
                ],
                [
                    63.53,
                    64.71,
                    62.94,
                    65.21
                ],
                [
                    64.78,
                    65.26,
                    64.77,
                    66.52
                ],
                [
                    65.0,
                    62.23,
                    61.67,
                    65.27
                ],
                [
                    62.22,
                    64.11,
                    62.2,
                    64.13
                ],
                [
                    64.08,
                    63.56,
                    62.9,
                    64.14
                ],
                [
                    63.82,
                    65.93,
                    63.5,
                    66.15
                ],
                [
                    66.0,
                    65.3,
                    64.78,
                    66.25
                ],
                [
                    64.3,
                    64.74,
                    64.08,
                    65.93
                ],
                [
                    64.83,
                    65.95,
                    63.84,
                    66.36
                ],
                [
                    66.31,
                    64.83,
                    63.56,
                    66.5
                ],
                [
                    64.22,
                    61.65,
                    61.58,
                    64.45
                ],
                [
                    61.67,
                    61.8,
                    61.14,
                    63.11
                ],
                [
                    61.39,
                    67.98,
                    61.28,
                    67.98
                ],
                [
                    70.06,
                    68.7,
                    68.2,
                    70.88
                ],
                [
                    69.26,
                    69.97,
                    68.65,
                    70.5
                ],
                [
                    69.5,
                    69.58,
                    69.18,
                    72.23
                ],
                [
                    69.82,
                    70.57,
                    69.25,
                    71.28
                ],
                [
                    69.11,
                    71.58,
                    68.88,
                    73.0
                ],
                [
                    71.65,
                    70.7,
                    70.66,
                    72.2
                ],
                [
                    71.1,
                    69.3,
                    68.8,
                    71.28
                ],
                [
                    69.06,
                    69.9,
                    68.92,
                    70.25
                ],
                [
                    69.66,
                    68.66,
                    68.34,
                    69.8
                ],
                [
                    68.8,
                    68.95,
                    68.7,
                    70.66
                ],
                [
                    68.45,
                    67.91,
                    67.66,
                    69.27
                ],
                [
                    67.84,
                    68.72,
                    67.65,
                    71.0
                ],
                [
                    68.12,
                    66.2,
                    66.0,
                    69.4
                ],
                [
                    66.02,
                    66.1,
                    64.56,
                    66.56
                ],
                [
                    66.03,
                    68.39,
                    65.55,
                    69.18
                ],
                [
                    67.8,
                    69.07,
                    67.5,
                    69.87
                ],
                [
                    68.5,
                    68.1,
                    67.75,
                    69.68
                ],
                [
                    68.0,
                    66.67,
                    66.15,
                    68.01
                ],
                [
                    66.03,
                    65.74,
                    64.91,
                    66.63
                ],
                [
                    66.0,
                    66.32,
                    65.75,
                    67.48
                ],
                [
                    66.2,
                    67.53,
                    65.9,
                    69.0
                ],
                [
                    66.88,
                    67.86,
                    66.81,
                    69.2
                ],
                [
                    64.5,
                    63.11,
                    61.07,
                    67.45
                ],
                [
                    63.99,
                    62.78,
                    61.41,
                    64.7
                ],
                [
                    62.0,
                    69.06,
                    61.37,
                    69.06
                ],
                [
                    69.49,
                    69.31,
                    68.1,
                    70.99
                ],
                [
                    68.8,
                    71.7,
                    68.7,
                    73.18
                ],
                [
                    72.15,
                    70.59,
                    70.3,
                    72.15
                ],
                [
                    70.44,
                    69.39,
                    68.45,
                    70.74
                ],
                [
                    69.09,
                    69.09,
                    68.01,
                    69.8
                ],
                [
                    68.86,
                    68.47,
                    68.44,
                    69.99
                ],
                [
                    68.16,
                    67.57,
                    66.88,
                    68.47
                ],
                [
                    67.7,
                    68.25,
                    67.29,
                    68.5
                ],
                [
                    68.0,
                    67.68,
                    67.51,
                    68.91
                ],
                [
                    67.8,
                    66.7,
                    66.53,
                    68.11
                ],
                [
                    66.8,
                    65.45,
                    65.27,
                    67.33
                ],
                [
                    65.7,
                    65.94,
                    65.0,
                    66.33
                ],
                [
                    65.97,
                    65.45,
                    65.16,
                    66.33
                ],
                [
                    63.51,
                    64.39,
                    62.83,
                    64.76
                ],
                [
                    64.3,
                    63.98,
                    63.9,
                    64.97
                ],
                [
                    64.3,
                    65.8,
                    64.2,
                    65.8
                ],
                [
                    66.42,
                    65.82,
                    65.38,
                    66.8
                ],
                [
                    65.72,
                    66.18,
                    65.3,
                    66.47
                ],
                [
                    66.18,
                    64.84,
                    64.72,
                    66.18
                ],
                [
                    65.2,
                    67.05,
                    65.2,
                    67.65
                ],
                [
                    67.1,
                    66.11,
                    66.03,
                    67.14
                ],
                [
                    66.11,
                    66.15,
                    65.7,
                    66.44
                ],
                [
                    66.09,
                    64.31,
                    64.31,
                    66.09
                ],
                [
                    64.2,
                    64.35,
                    63.76,
                    64.82
                ],
                [
                    64.02,
                    64.68,
                    63.83,
                    64.91
                ],
                [
                    64.7,
                    64.63,
                    64.31,
                    64.97
                ],
                [
                    64.62,
                    64.39,
                    64.32,
                    64.95
                ],
                [
                    64.09,
                    64.57,
                    64.06,
                    65.45
                ],
                [
                    64.5,
                    63.08,
                    63.03,
                    64.73
                ],
                [
                    63.31,
                    64.46,
                    63.1,
                    64.5
                ],
                [
                    64.42,
                    64.15,
                    63.89,
                    65.07
                ],
                [
                    64.21,
                    63.4,
                    63.22,
                    64.41
                ],
                [
                    63.76,
                    64.0,
                    63.5,
                    64.5
                ],
                [
                    63.97,
                    63.97,
                    63.0,
                    64.25
                ],
                [
                    63.4,
                    64.24,
                    63.17,
                    64.44
                ],
                [
                    64.2,
                    64.25,
                    64.04,
                    64.5
                ],
                [
                    64.5,
                    64.84,
                    64.06,
                    65.1
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                231688.96,
                145421.73,
                157984.89,
                124180.68,
                176961.79,
                125330.32,
                148874.97,
                179335.16,
                190248.74,
                120084.63,
                193759.83,
                381248.8,
                377863.86,
                346696.51,
                236280.5,
                191101.32,
                258566.72,
                279449.93,
                186140.61,
                253623.68,
                243911.65,
                154916.64,
                105425.17,
                123137.9,
                162552.34,
                143662.85,
                134610.43,
                102079.68,
                246215.76,
                146481.38,
                143253.03,
                233294.42,
                143638.77,
                124028.05,
                120202.94,
                155459.57,
                118006.18,
                120202.6,
                199291.63,
                241698.38,
                338283.84,
                227460.65,
                214322.51,
                282246.57,
                196995.69,
                197339.72,
                204101.6,
                282649.0,
                218686.3,
                156368.32,
                400118.09,
                260787.97,
                207206.75,
                301546.97,
                273293.56,
                281880.67,
                163038.23,
                510562.48,
                916376.14,
                576104.66,
                611093.98,
                429639.71,
                462314.25,
                364324.68,
                329686.26,
                275096.71,
                253950.77,
                246643.23,
                200792.85,
                366339.18,
                289513.52,
                192901.94,
                353595.99,
                226343.55,
                193127.68,
                166737.43,
                145153.96,
                143643.97,
                181232.91,
                186208.83,
                398774.36,
                253115.17,
                588756.44,
                673791.25,
                729692.84,
                371926.4,
                266688.8,
                205872.88,
                242795.78,
                194872.19,
                151224.39,
                169319.75,
                223167.55,
                178143.39,
                124927.38,
                103477.74,
                195295.1,
                164986.54,
                172964.99,
                163227.13,
                125929.02,
                120711.44,
                232091.89,
                143491.76,
                101564.94,
                117930.1,
                99959.15,
                88159.0,
                66409.1,
                59207.9,
                105842.19,
                103306.5,
                92837.53,
                67805.34,
                60645.68,
                112713.54,
                89098.69,
                80537.97,
                67668.58,
                104033.68
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7f7f7f"
            }
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-04",
                    -4.22
                ],
                [
                    "2024-12-05",
                    -14.82
                ],
                [
                    "2024-12-06",
                    3.74
                ],
                [
                    "2024-12-09",
                    -13.09
                ],
                [
                    "2024-12-10",
                    -3.39
                ],
                [
                    "2024-12-11",
                    -6.75
                ],
                [
                    "2024-12-12",
                    1.67
                ],
                [
                    "2024-12-13",
                    -8.18
                ],
                [
                    "2024-12-16",
                    0.29
                ],
                [
                    "2024-12-17",
                    -7.29
                ],
                [
                    "2024-12-18",
                    4.09
                ],
                [
                    "2024-12-19",
                    8.46
                ],
                [
                    "2024-12-20",
                    0.91
                ],
                [
                    "2024-12-23",
                    -4.71
                ],
                [
                    "2024-12-24",
                    -7.49
                ],
                [
                    "2024-12-25",
                    -5.87
                ],
                [
                    "2024-12-26",
                    8.72
                ],
                [
                    "2024-12-27",
                    -3.83
                ],
                [
                    "2024-12-30",
                    -8.47
                ],
                [
                    "2024-12-31",
                    -19.25
                ],
                [
                    "2025-01-02",
                    -15.16
                ],
                [
                    "2025-01-03",
                    -9.18
                ],
                [
                    "2025-01-06",
                    -11.09
                ],
                [
                    "2025-01-07",
                    1.04
                ],
                [
                    "2025-01-08",
                    -8.48
                ],
                [
                    "2025-01-09",
                    8.03
                ],
                [
                    "2025-01-10",
                    -6.01
                ],
                [
                    "2025-01-13",
                    -8.41
                ],
                [
                    "2025-01-14",
                    0.73
                ],
                [
                    "2025-01-15",
                    -6.3
                ],
                [
                    "2025-01-16",
                    -5.86
                ],
                [
                    "2025-01-17",
                    5.73
                ],
                [
                    "2025-01-20",
                    -14.16
                ],
                [
                    "2025-01-21",
                    -0.81
                ],
                [
                    "2025-01-22",
                    -4.22
                ],
                [
                    "2025-01-23",
                    -5.49
                ],
                [
                    "2025-01-24",
                    1.28
                ],
                [
                    "2025-01-27",
                    -4.67
                ],
                [
                    "2025-02-05",
                    1.56
                ],
                [
                    "2025-02-06",
                    5.45
                ],
                [
                    "2025-02-07",
                    3.12
                ],
                [
                    "2025-02-10",
                    -3.97
                ],
                [
                    "2025-02-11",
                    5.07
                ],
                [
                    "2025-02-12",
                    5.26
                ],
                [
                    "2025-02-13",
                    -11.39
                ],
                [
                    "2025-02-14",
                    3.82
                ],
                [
                    "2025-02-17",
                    1.78
                ],
                [
                    "2025-02-18",
                    -10.25
                ],
                [
                    "2025-02-19",
                    2.8
                ],
                [
                    "2025-02-20",
                    -9.19
                ],
                [
                    "2025-02-21",
                    11.12
                ],
                [
                    "2025-02-24",
                    -4.18
                ],
                [
                    "2025-02-25",
                    -2.61
                ],
                [
                    "2025-02-26",
                    7.6
                ],
                [
                    "2025-02-27",
                    -11.24
                ],
                [
                    "2025-02-28",
                    -18.76
                ],
                [
                    "2025-03-03",
                    -5.46
                ],
                [
                    "2025-03-04",
                    26.68
                ],
                [
                    "2025-03-05",
                    -7.23
                ],
                [
                    "2025-03-06",
                    2.78
                ],
                [
                    "2025-03-07",
                    -4.29
                ],
                [
                    "2025-03-10",
                    4.03
                ],
                [
                    "2025-03-11",
                    3.26
                ],
                [
                    "2025-03-12",
                    -4.4
                ],
                [
                    "2025-03-13",
                    -7.77
                ],
                [
                    "2025-03-14",
                    3.74
                ],
                [
                    "2025-03-17",
                    -10.75
                ],
                [
                    "2025-03-18",
                    0.95
                ],
                [
                    "2025-03-19",
                    -13.84
                ],
                [
                    "2025-03-20",
                    3.85
                ],
                [
                    "2025-03-21",
                    -19.72
                ],
                [
                    "2025-03-24",
                    -6.01
                ],
                [
                    "2025-03-25",
                    17.39
                ],
                [
                    "2025-03-26",
                    0.76
                ],
                [
                    "2025-03-27",
                    -0.96
                ],
                [
                    "2025-03-28",
                    -18.54
                ],
                [
                    "2025-03-31",
                    -12.77
                ],
                [
                    "2025-04-01",
                    2.27
                ],
                [
                    "2025-04-02",
                    7.49
                ],
                [
                    "2025-04-03",
                    3.56
                ],
                [
                    "2025-04-07",
                    -8.31
                ],
                [
                    "2025-04-08",
                    -6.84
                ],
                [
                    "2025-04-09",
                    19.78
                ],
                [
                    "2025-04-10",
                    -7.89
                ],
                [
                    "2025-04-11",
                    4.98
                ],
                [
                    "2025-04-14",
                    -6.19
                ],
                [
                    "2025-04-15",
                    -10.19
                ],
                [
                    "2025-04-16",
                    -2.58
                ],
                [
                    "2025-04-17",
                    -2.92
                ],
                [
                    "2025-04-18",
                    -10.4
                ],
                [
                    "2025-04-21",
                    6.69
                ],
                [
                    "2025-04-22",
                    -2.51
                ],
                [
                    "2025-04-23",
                    -10.8
                ],
                [
                    "2025-04-24",
                    -16.22
                ],
                [
                    "2025-04-25",
                    2.59
                ],
                [
                    "2025-04-28",
                    -6.26
                ],
                [
                    "2025-04-29",
                    -12.88
                ],
                [
                    "2025-04-30",
                    -15.62
                ],
                [
                    "2025-05-06",
                    4.78
                ],
                [
                    "2025-05-07",
                    -4.86
                ],
                [
                    "2025-05-08",
                    0.39
                ],
                [
                    "2025-05-09",
                    -15.95
                ],
                [
                    "2025-05-12",
                    15.48
                ],
                [
                    "2025-05-13",
                    -17.1
                ],
                [
                    "2025-05-14",
                    -7.33
                ],
                [
                    "2025-05-15",
                    -20.33
                ],
                [
                    "2025-05-16",
                    -11.28
                ],
                [
                    "2025-05-19",
                    -2.73
                ],
                [
                    "2025-05-20",
                    -5.98
                ],
                [
                    "2025-05-21",
                    -11.17
                ],
                [
                    "2025-05-22",
                    -1.57
                ],
                [
                    "2025-05-23",
                    -20.6
                ],
                [
                    "2025-05-26",
                    2.87
                ],
                [
                    "2025-05-27",
                    0.0
                ],
                [
                    "2025-05-28",
                    -10.59
                ],
                [
                    "2025-05-29",
                    -1.68
                ],
                [
                    "2025-05-30",
                    -13.86
                ],
                [
                    "2025-06-03",
                    5.39
                ],
                [
                    "2025-06-04",
                    -3.49
                ],
                [
                    "2025-06-05",
                    2.68
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5470C6"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "symbol": "triangle",
            "symbolSize": 12,
            "data": [
                [
                    "2024-12-04",
                    null
                ],
                [
                    "2024-12-05",
                    null
                ],
                [
                    "2024-12-06",
                    null
                ],
                [
                    "2024-12-09",
                    null
                ],
                [
                    "2024-12-10",
                    null
                ],
                [
                    "2024-12-11",
                    null
                ],
                [
                    "2024-12-12",
                    null
                ],
                [
                    "2024-12-13",
                    null
                ],
                [
                    "2024-12-16",
                    null
                ],
                [
                    "2024-12-17",
                    null
                ],
                [
                    "2024-12-18",
                    null
                ],
                [
                    "2024-12-19",
                    null
                ],
                [
                    "2024-12-20",
                    null
                ],
                [
                    "2024-12-23",
                    null
                ],
                [
                    "2024-12-24",
                    null
                ],
                [
                    "2024-12-25",
                    null
                ],
                [
                    "2024-12-26",
                    null
                ],
                [
                    "2024-12-27",
                    null
                ],
                [
                    "2024-12-30",
                    null
                ],
                [
                    "2024-12-31",
                    null
                ],
                [
                    "2025-01-02",
                    null
                ],
                [
                    "2025-01-03",
                    null
                ],
                [
                    "2025-01-06",
                    null
                ],
                [
                    "2025-01-07",
                    null
                ],
                [
                    "2025-01-08",
                    null
                ],
                [
                    "2025-01-09",
                    null
                ],
                [
                    "2025-01-10",
                    null
                ],
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    null
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    null
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    null
                ],
                [
                    "2025-02-26",
                    null
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    null
                ],
                [
                    "2025-03-04",
                    null
                ],
                [
                    "2025-03-05",
                    null
                ],
                [
                    "2025-03-06",
                    null
                ],
                [
                    "2025-03-07",
                    null
                ],
                [
                    "2025-03-10",
                    null
                ],
                [
                    "2025-03-11",
                    null
                ],
                [
                    "2025-03-12",
                    null
                ],
                [
                    "2025-03-13",
                    null
                ],
                [
                    "2025-03-14",
                    null
                ],
                [
                    "2025-03-17",
                    null
                ],
                [
                    "2025-03-18",
                    null
                ],
                [
                    "2025-03-19",
                    null
                ],
                [
                    "2025-03-20",
                    null
                ],
                [
                    "2025-03-21",
                    null
                ],
                [
                    "2025-03-24",
                    null
                ],
                [
                    "2025-03-25",
                    null
                ],
                [
                    "2025-03-26",
                    null
                ],
                [
                    "2025-03-27",
                    null
                ],
                [
                    "2025-03-28",
                    null
                ],
                [
                    "2025-03-31",
                    null
                ],
                [
                    "2025-04-01",
                    null
                ],
                [
                    "2025-04-02",
                    null
                ],
                [
                    "2025-04-03",
                    null
                ],
                [
                    "2025-04-07",
                    null
                ],
                [
                    "2025-04-08",
                    null
                ],
                [
                    "2025-04-09",
                    null
                ],
                [
                    "2025-04-10",
                    null
                ],
                [
                    "2025-04-11",
                    null
                ],
                [
                    "2025-04-14",
                    null
                ],
                [
                    "2025-04-15",
                    null
                ],
                [
                    "2025-04-16",
                    null
                ],
                [
                    "2025-04-17",
                    null
                ],
                [
                    "2025-04-18",
                    null
                ],
                [
                    "2025-04-21",
                    null
                ],
                [
                    "2025-04-22",
                    null
                ],
                [
                    "2025-04-23",
                    null
                ],
                [
                    "2025-04-24",
                    null
                ],
                [
                    "2025-04-25",
                    null
                ],
                [
                    "2025-04-28",
                    null
                ],
                [
                    "2025-04-29",
                    null
                ],
                [
                    "2025-04-30",
                    null
                ],
                [
                    "2025-05-06",
                    null
                ],
                [
                    "2025-05-07",
                    null
                ],
                [
                    "2025-05-08",
                    null
                ],
                [
                    "2025-05-09",
                    null
                ],
                [
                    "2025-05-12",
                    null
                ],
                [
                    "2025-05-13",
                    null
                ],
                [
                    "2025-05-14",
                    null
                ],
                [
                    "2025-05-15",
                    null
                ],
                [
                    "2025-05-16",
                    null
                ],
                [
                    "2025-05-19",
                    null
                ],
                [
                    "2025-05-20",
                    null
                ],
                [
                    "2025-05-21",
                    null
                ],
                [
                    "2025-05-22",
                    null
                ],
                [
                    "2025-05-23",
                    null
                ],
                [
                    "2025-05-26",
                    null
                ],
                [
                    "2025-05-27",
                    null
                ],
                [
                    "2025-05-28",
                    null
                ],
                [
                    "2025-05-29",
                    null
                ],
                [
                    "2025-05-30",
                    null
                ],
                [
                    "2025-06-03",
                    null
                ],
                [
                    "2025-06-04",
                    null
                ],
                [
                    "2025-06-05",
                    null
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "purple"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002049 \u4e3b\u529b\u5165\u573a\u4fe1\u53f7 K \u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "50%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_10ff3e817f9d49c8a8a01a6c4e87f323.setOption(option_10ff3e817f9d49c8a8a01a6c4e87f323);
    </script>
</body>
</html>
