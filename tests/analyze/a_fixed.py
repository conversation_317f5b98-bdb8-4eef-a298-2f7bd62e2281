import pandas as pd
import numpy as np
from pyecharts.charts import Line, Scatter, Grid, Kline
from pyecharts import options as opts

code = "002049"
daily_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
fund_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"
daily_data = pd.read_csv(daily_path)
funds_data = pd.read_csv(fund_path)

# 滚动窗口长度，用作背离判断
window = 5

# === 1. 读取并预处理日线数据 ===
daily = daily_data.rename(columns={
    "trade_date": "date",
    "open": "open",
    "high": "high",
    "low": "low",
    "close": "close",
    "vol": "vol"
})[["date", "open", "high", "low", "close", "vol"]]

# 转换日期格式，并设为索引后按日期排序
daily["date"] = pd.to_datetime(daily["date"], format="%Y%m%d")
daily = daily.set_index("date").sort_index()

# 准备 K 线图所需的数据：日期列表 + [(开盘, 收盘, 最低, 最高), ...] 列表
kline_dates = [d.strftime("%Y-%m-%d") for d in daily.index]
kline_values = daily[["open", "close", "low", "high"]].round(2).values.tolist()

# === 2. 读取并预处理资金流数据 ===
funds = funds_data.rename(columns={
    "日期": "date",
    "超大单净流入-净占比": "elg_net_pct",
    "大单净流入-净占比": "large_net_pct",
    "中单净流入-净占比": "medium_net_pct",
    "小单净流入-净占比": "small_net_pct"
})[["date", "elg_net_pct", "large_net_pct", "medium_net_pct", "small_net_pct"]]

funds["date"] = pd.to_datetime(funds["date"], format="%Y-%m-%d")
funds = funds.set_index("date").sort_index()

# 计算三条净占比：主力（大单+超大单）、游资（中单）、散户（小单）
funds["main_net_pct"] = funds["elg_net_pct"] + funds["large_net_pct"]
funds["heat_net_pct"] = funds["medium_net_pct"]
funds["retail_net_pct"] = funds["small_net_pct"]

# 计算滚动均值，用于背离判断
funds["main_trend"] = funds["main_net_pct"].rolling(window).mean()
funds["retail_trend"] = funds["retail_net_pct"].rolling(window).mean()
funds["heat_trend"] = funds["heat_net_pct"].rolling(window).mean()

# 找出各种背离日期
mask_main = (funds["main_trend"] > 0) & (funds["retail_trend"] < 0)
mask_heat = (funds["heat_trend"] > 0) & (funds["retail_trend"] < 0)
mask_retail = (funds["retail_trend"] > 0) & (funds["main_trend"] < 0)

div_main_dates = funds[mask_main].index.tolist()
div_heat_dates = funds[mask_heat].index.tolist()
div_retail_dates = funds[mask_retail].index.tolist()

# 转成字符串
all_flow_dates = [d.strftime("%Y-%m-%d") for d in funds.index]
div_main_str = [d.strftime("%Y-%m-%d") for d in div_main_dates]
div_heat_str = [d.strftime("%Y-%m-%d") for d in div_heat_dates]
div_retail_str = [d.strftime("%Y-%m-%d") for d in div_retail_dates]

# 对应背离点的净占比数值
div_main_vals = funds.loc[div_main_dates, "main_net_pct"].round(2).tolist()
div_heat_vals = funds.loc[div_heat_dates, "heat_net_pct"].round(2).tolist()
div_retail_vals = funds.loc[div_retail_dates, "retail_net_pct"].round(2).tolist()

# 三条折线的完整数据
main_vals = funds["main_net_pct"].round(2).tolist()
heat_vals = funds["heat_net_pct"].round(2).tolist()
retail_vals = funds["retail_net_pct"].round(2).tolist()

# === 3. 绘制 K 线图 ===
kline = (
    Kline(init_opts=opts.InitOpts(width="100%", height="400px"))
    .add_xaxis(kline_dates)
    .add_yaxis(
        series_name="K线",
        y_axis=kline_values,
        itemstyle_opts=opts.ItemStyleOpts(color="#ec0000", color0="#00da3c")
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="日线K线图（联动资金流）"),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="cross",
            is_show=True
        ),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            is_scale=True,
            boundary_gap=False,
            axisline_opts=opts.AxisLineOpts(is_on_zero=False),
            splitline_opts=opts.SplitLineOpts(is_show=False),
            axislabel_opts=opts.LabelOpts(is_show=False)
        ),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            splitline_opts=opts.SplitLineOpts(is_show=True)
        ),
        datazoom_opts=[
            opts.DataZoomOpts(
                type_="inside", 
                xaxis_index=[0, 1], 
                range_start=0, 
                range_end=100
            ),
            opts.DataZoomOpts(
                is_show=True, 
                xaxis_index=[0, 1], 
                range_start=0, 
                range_end=100
            ),
        ],
    )
)

# === 4. 绘制资金流折线图 ===
flow_line = (
    Line(init_opts=opts.InitOpts(width="100%", height="300px"))
    .add_xaxis(all_flow_dates)
    .add_yaxis(
        series_name="主力净占比",
        y_axis=main_vals,
        is_smooth=True,
        linestyle_opts=opts.LineStyleOpts(color="#1f77b4", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )
    .add_yaxis(
        series_name="游资净占比",
        y_axis=heat_vals,
        is_smooth=True,
        linestyle_opts=opts.LineStyleOpts(color="#9467bd", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )
    .add_yaxis(
        series_name="散户净占比",
        y_axis=retail_vals,
        is_smooth=True,
        linestyle_opts=opts.LineStyleOpts(color="#ff7f0e", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="资金流对比与背离标注"),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="cross",
            is_show=True
        ),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            boundary_gap=False,
            axislabel_opts=opts.LabelOpts(rotate=-45)
        ),
        yaxis_opts=opts.AxisOpts(name="净占比 (%)", is_scale=True),
        datazoom_opts=[
            opts.DataZoomOpts(
                type_="slider", 
                xaxis_index=[0, 1], 
                range_start=0, 
                range_end=100
            )
        ],
        legend_opts=opts.LegendOpts(pos_top="5%")
    )
)

# === 5. 添加背离点散点图 ===
if div_main_str:  # 只有当有背离点时才添加
    scatter_main = (
        Scatter()
        .add_xaxis(div_main_str)
        .add_yaxis(
            series_name="主力背离",
            y_axis=div_main_vals,
            symbol="triangle",
            symbol_size=15,
            itemstyle_opts=opts.ItemStyleOpts(color="red")
        )
    )
    flow_line = flow_line.overlap(scatter_main)

if div_heat_str:
    scatter_heat = (
        Scatter()
        .add_xaxis(div_heat_str)
        .add_yaxis(
            series_name="游资背离",
            y_axis=div_heat_vals,
            symbol="diamond",
            symbol_size=15,
            itemstyle_opts=opts.ItemStyleOpts(color="gold")
        )
    )
    flow_line = flow_line.overlap(scatter_heat)

if div_retail_str:
    scatter_retail = (
        Scatter()
        .add_xaxis(div_retail_str)
        .add_yaxis(
            series_name="散户背离",
            y_axis=div_retail_vals,
            symbol="circle",
            symbol_size=15,
            itemstyle_opts=opts.ItemStyleOpts(color="black")
        )
    )
    flow_line = flow_line.overlap(scatter_retail)

# === 6. 用 Grid 将两张图合并并实现联动 ===
grid = (
    Grid(init_opts=opts.InitOpts(width="100%", height="750px"))
    .add(
        kline, 
        grid_opts=opts.GridOpts(
            pos_left="5%", 
            pos_right="5%", 
            pos_top="5%", 
            height="45%"
        )
    )
    .add(
        flow_line, 
        grid_opts=opts.GridOpts(
            pos_left="5%", 
            pos_right="5%", 
            pos_top="55%", 
            height="40%"
        )
    )
)

# 7. 渲染成 HTML
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_fund_flow_divergence_fixed.html"
grid.render(html_path)
print(f"图表已生成: {html_path}")
