<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="14b97e25757a4c72a3fa5bd278622c2e" class="chart-container" style="width:1000px; height:700px; "></div>
    <script>
        var chart_14b97e25757a4c72a3fa5bd278622c2e = echarts.init(
            document.getElementById('14b97e25757a4c72a3fa5bd278622c2e'), 'white', {renderer: 'canvas'});
        var option_14b97e25757a4c72a3fa5bd278622c2e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    22.28,
                    22.12,
                    22.0,
                    22.47
                ],
                [
                    22.03,
                    22.15,
                    22.01,
                    22.19
                ],
                [
                    22.1,
                    22.21,
                    21.98,
                    22.24
                ],
                [
                    22.45,
                    22.65,
                    22.44,
                    22.88
                ],
                [
                    23.0,
                    22.5,
                    22.47,
                    23.12
                ],
                [
                    22.65,
                    22.68,
                    22.52,
                    22.78
                ],
                [
                    22.78,
                    22.9,
                    22.74,
                    23.12
                ],
                [
                    22.7,
                    22.43,
                    22.37,
                    22.7
                ],
                [
                    22.35,
                    22.17,
                    22.08,
                    22.46
                ],
                [
                    22.2,
                    22.0,
                    21.92,
                    22.23
                ],
                [
                    22.1,
                    21.84,
                    21.8,
                    22.19
                ],
                [
                    21.72,
                    21.53,
                    21.25,
                    21.72
                ],
                [
                    21.4,
                    21.5,
                    21.38,
                    21.63
                ],
                [
                    21.5,
                    21.13,
                    21.07,
                    21.6
                ],
                [
                    21.06,
                    21.18,
                    20.9,
                    21.19
                ],
                [
                    21.18,
                    20.98,
                    20.92,
                    21.2
                ],
                [
                    21.0,
                    21.26,
                    20.99,
                    21.35
                ],
                [
                    21.21,
                    21.3,
                    21.15,
                    21.39
                ],
                [
                    21.3,
                    21.09,
                    21.01,
                    21.3
                ],
                [
                    21.1,
                    20.88,
                    20.74,
                    21.23
                ],
                [
                    20.88,
                    21.09,
                    20.78,
                    21.5
                ],
                [
                    21.37,
                    22.47,
                    21.28,
                    23.01
                ],
                [
                    22.0,
                    22.02,
                    21.34,
                    22.63
                ],
                [
                    21.8,
                    22.23,
                    21.61,
                    22.43
                ],
                [
                    22.0,
                    22.32,
                    21.9,
                    22.74
                ],
                [
                    22.04,
                    22.48,
                    21.8,
                    22.7
                ],
                [
                    22.88,
                    22.07,
                    22.01,
                    23.0
                ],
                [
                    21.76,
                    22.22,
                    21.45,
                    22.48
                ],
                [
                    22.35,
                    22.19,
                    21.78,
                    22.35
                ],
                [
                    22.11,
                    21.89,
                    21.54,
                    22.15
                ],
                [
                    21.99,
                    22.02,
                    21.87,
                    22.5
                ],
                [
                    22.0,
                    21.83,
                    21.74,
                    22.28
                ],
                [
                    21.71,
                    21.63,
                    21.37,
                    21.82
                ],
                [
                    21.55,
                    21.58,
                    21.31,
                    21.72
                ],
                [
                    21.7,
                    21.94,
                    21.56,
                    22.1
                ],
                [
                    21.85,
                    21.66,
                    21.64,
                    22.1
                ],
                [
                    21.58,
                    21.8,
                    21.33,
                    21.8
                ],
                [
                    21.8,
                    21.58,
                    21.52,
                    22.0
                ],
                [
                    22.08,
                    22.76,
                    21.9,
                    22.8
                ],
                [
                    22.63,
                    22.45,
                    22.33,
                    22.63
                ],
                [
                    22.4,
                    22.51,
                    22.28,
                    22.73
                ],
                [
                    22.66,
                    23.16,
                    22.49,
                    23.28
                ],
                [
                    23.6,
                    23.33,
                    23.15,
                    24.06
                ],
                [
                    22.69,
                    22.69,
                    22.49,
                    22.88
                ],
                [
                    22.69,
                    22.56,
                    22.5,
                    22.97
                ],
                [
                    22.74,
                    22.48,
                    22.37,
                    22.8
                ],
                [
                    21.7,
                    21.78,
                    21.63,
                    21.97
                ],
                [
                    21.84,
                    21.78,
                    21.67,
                    22.02
                ],
                [
                    22.1,
                    21.9,
                    21.8,
                    22.16
                ],
                [
                    21.84,
                    21.94,
                    21.7,
                    22.02
                ],
                [
                    21.9,
                    21.56,
                    21.5,
                    21.98
                ],
                [
                    21.44,
                    21.96,
                    21.15,
                    22.11
                ],
                [
                    21.95,
                    21.67,
                    21.64,
                    22.08
                ],
                [
                    21.68,
                    21.73,
                    21.63,
                    22.3
                ],
                [
                    21.73,
                    21.82,
                    21.56,
                    22.13
                ],
                [
                    21.7,
                    21.39,
                    21.38,
                    21.79
                ],
                [
                    21.53,
                    21.34,
                    21.24,
                    21.65
                ],
                [
                    21.43,
                    21.71,
                    21.36,
                    21.73
                ],
                [
                    21.63,
                    21.89,
                    21.6,
                    21.92
                ],
                [
                    21.83,
                    21.89,
                    21.7,
                    21.99
                ],
                [
                    21.73,
                    22.13,
                    21.72,
                    22.45
                ],
                [
                    22.4,
                    22.05,
                    21.92,
                    22.4
                ],
                [
                    21.84,
                    22.11,
                    21.7,
                    22.16
                ],
                [
                    22.12,
                    22.07,
                    22.04,
                    22.39
                ],
                [
                    22.21,
                    22.15,
                    21.93,
                    22.31
                ],
                [
                    22.98,
                    22.34,
                    22.24,
                    23.1
                ],
                [
                    22.3,
                    22.2,
                    22.17,
                    22.51
                ],
                [
                    22.3,
                    22.79,
                    22.2,
                    22.99
                ],
                [
                    22.79,
                    23.3,
                    22.71,
                    23.54
                ],
                [
                    23.58,
                    22.89,
                    22.83,
                    23.73
                ],
                [
                    22.71,
                    22.38,
                    22.23,
                    22.98
                ],
                [
                    22.38,
                    22.18,
                    21.86,
                    22.43
                ],
                [
                    22.1,
                    22.35,
                    21.98,
                    22.8
                ],
                [
                    22.36,
                    22.35,
                    22.17,
                    22.72
                ],
                [
                    22.25,
                    22.35,
                    22.03,
                    22.48
                ],
                [
                    22.9,
                    23.84,
                    22.49,
                    24.14
                ],
                [
                    23.69,
                    24.88,
                    23.51,
                    25.0
                ],
                [
                    24.44,
                    23.91,
                    23.8,
                    24.79
                ],
                [
                    23.74,
                    23.09,
                    22.98,
                    23.84
                ],
                [
                    23.6,
                    24.22,
                    23.17,
                    24.5
                ],
                [
                    21.82,
                    21.8,
                    21.8,
                    22.95
                ],
                [
                    21.45,
                    21.58,
                    20.88,
                    21.98
                ],
                [
                    21.0,
                    21.82,
                    19.8,
                    21.99
                ],
                [
                    22.3,
                    24.0,
                    22.17,
                    24.0
                ],
                [
                    25.38,
                    26.4,
                    25.3,
                    26.4
                ],
                [
                    25.33,
                    26.53,
                    25.33,
                    27.0
                ],
                [
                    26.11,
                    25.79,
                    25.41,
                    26.45
                ],
                [
                    26.52,
                    26.43,
                    25.65,
                    26.72
                ],
                [
                    27.28,
                    25.11,
                    25.05,
                    27.49
                ],
                [
                    25.11,
                    24.47,
                    24.26,
                    25.5
                ],
                [
                    25.11,
                    26.92,
                    24.99,
                    26.92
                ],
                [
                    27.3,
                    28.56,
                    27.04,
                    29.38
                ],
                [
                    26.2,
                    25.9,
                    25.83,
                    27.5
                ],
                [
                    26.97,
                    25.84,
                    25.69,
                    26.99
                ],
                [
                    26.1,
                    24.81,
                    24.52,
                    26.22
                ],
                [
                    24.33,
                    24.19,
                    23.93,
                    24.48
                ],
                [
                    24.49,
                    24.33,
                    24.15,
                    24.69
                ],
                [
                    24.15,
                    24.08,
                    23.85,
                    24.65
                ],
                [
                    24.77,
                    25.8,
                    24.72,
                    26.26
                ],
                [
                    25.1,
                    25.9,
                    25.1,
                    26.0
                ],
                [
                    25.78,
                    25.4,
                    25.27,
                    26.27
                ],
                [
                    24.9,
                    25.12,
                    24.58,
                    25.31
                ],
                [
                    24.31,
                    24.34,
                    23.92,
                    24.45
                ],
                [
                    24.34,
                    24.44,
                    23.94,
                    24.55
                ],
                [
                    24.2,
                    24.06,
                    23.9,
                    24.42
                ],
                [
                    23.62,
                    23.66,
                    23.35,
                    23.89
                ],
                [
                    24.01,
                    23.71,
                    23.65,
                    24.24
                ],
                [
                    23.99,
                    23.93,
                    23.6,
                    24.35
                ],
                [
                    23.76,
                    23.96,
                    23.76,
                    24.16
                ],
                [
                    24.72,
                    25.4,
                    24.24,
                    25.4
                ],
                [
                    24.78,
                    24.25,
                    24.09,
                    25.28
                ],
                [
                    23.81,
                    24.8,
                    23.52,
                    25.46
                ],
                [
                    24.0,
                    24.43,
                    23.92,
                    24.71
                ],
                [
                    24.18,
                    23.7,
                    23.68,
                    24.3
                ],
                [
                    23.6,
                    23.77,
                    23.56,
                    24.0
                ],
                [
                    23.34,
                    23.34,
                    23.01,
                    23.44
                ],
                [
                    23.53,
                    23.24,
                    23.12,
                    23.59
                ],
                [
                    23.82,
                    24.17,
                    23.81,
                    24.62
                ],
                [
                    23.8,
                    24.12,
                    23.8,
                    24.39
                ],
                [
                    24.18,
                    23.91,
                    23.81,
                    24.3
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                48913.3,
                30674.0,
                40644.71,
                90272.0,
                77215.71,
                49963.0,
                73705.0,
                50525.0,
                35181.0,
                34507.95,
                32611.0,
                55980.97,
                31508.0,
                44446.0,
                39427.53,
                24563.0,
                24319.0,
                24443.0,
                21029.75,
                31644.82,
                66017.99,
                178697.5,
                124618.85,
                94184.99,
                103175.14,
                101640.5,
                94911.69,
                103385.0,
                120025.0,
                99721.65,
                107643.64,
                85884.99,
                66427.99,
                50161.0,
                107454.62,
                83454.99,
                58727.0,
                55813.99,
                177909.53,
                116564.0,
                117806.0,
                157640.89,
                236041.0,
                136231.55,
                87096.01,
                76587.0,
                90309.0,
                77069.0,
                62832.08,
                105009.92,
                88456.69,
                97617.88,
                67049.0,
                77905.61,
                84863.66,
                59980.0,
                43639.0,
                64666.0,
                57713.71,
                47348.0,
                88677.0,
                50767.0,
                44538.01,
                46452.7,
                60061.0,
                152234.01,
                75445.0,
                198929.0,
                214742.66,
                173258.69,
                96461.65,
                79510.42,
                73820.07,
                78753.45,
                54552.45,
                310062.6,
                338288.35,
                259592.97,
                181341.77,
                301438.05,
                202635.01,
                185374.06,
                182032.59,
                217004.4,
                594155.48,
                524082.93,
                275735.96,
                374306.76,
                400937.95,
                202645.95,
                361003.61,
                484876.41,
                401778.27,
                264620.0,
                240618.57,
                145107.99,
                145312.01,
                121135.09,
                279739.42,
                224149.41,
                205642.13,
                135109.0,
                131635.0,
                162374.0,
                127352.0,
                126303.42,
                106387.9,
                106711.07,
                95884.0,
                281371.71,
                270937.07,
                248714.33,
                150808.32,
                134622.57,
                76464.0,
                116139.4,
                69926.0,
                217677.5,
                144355.98,
                104817.0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7f7f7f"
            }
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-04",
                    -3.16
                ],
                [
                    "2024-12-05",
                    -14.61
                ],
                [
                    "2024-12-06",
                    -10.32
                ],
                [
                    "2024-12-09",
                    1.65
                ],
                [
                    "2024-12-10",
                    -4.05
                ],
                [
                    "2024-12-11",
                    -9.83
                ],
                [
                    "2024-12-12",
                    -2.17
                ],
                [
                    "2024-12-13",
                    -11.32
                ],
                [
                    "2024-12-16",
                    -1.63
                ],
                [
                    "2024-12-17",
                    3.03
                ],
                [
                    "2024-12-18",
                    0.02
                ],
                [
                    "2024-12-19",
                    -4.75
                ],
                [
                    "2024-12-20",
                    -2.19
                ],
                [
                    "2024-12-23",
                    5.24
                ],
                [
                    "2024-12-24",
                    -2.72
                ],
                [
                    "2024-12-25",
                    -8.76
                ],
                [
                    "2024-12-26",
                    6.83
                ],
                [
                    "2024-12-27",
                    8.37
                ],
                [
                    "2024-12-30",
                    -1.46
                ],
                [
                    "2024-12-31",
                    -4.9
                ],
                [
                    "2025-01-02",
                    3.75
                ],
                [
                    "2025-01-03",
                    -0.89
                ],
                [
                    "2025-01-06",
                    1.24
                ],
                [
                    "2025-01-07",
                    -1.22
                ],
                [
                    "2025-01-08",
                    -0.13
                ],
                [
                    "2025-01-09",
                    7.15
                ],
                [
                    "2025-01-10",
                    3.19
                ],
                [
                    "2025-01-13",
                    1.33
                ],
                [
                    "2025-01-14",
                    -10.38
                ],
                [
                    "2025-01-15",
                    -8.81
                ],
                [
                    "2025-01-16",
                    -0.85
                ],
                [
                    "2025-01-17",
                    -10.58
                ],
                [
                    "2025-01-20",
                    -11.12
                ],
                [
                    "2025-01-21",
                    -11.93
                ],
                [
                    "2025-01-22",
                    11.43
                ],
                [
                    "2025-01-23",
                    -6.59
                ],
                [
                    "2025-01-24",
                    -1.7
                ],
                [
                    "2025-01-27",
                    0.34
                ],
                [
                    "2025-02-05",
                    1.33
                ],
                [
                    "2025-02-06",
                    -9.06
                ],
                [
                    "2025-02-07",
                    -3.87
                ],
                [
                    "2025-02-10",
                    5.77
                ],
                [
                    "2025-02-11",
                    0.44
                ],
                [
                    "2025-02-12",
                    -11.62
                ],
                [
                    "2025-02-13",
                    -4.81
                ],
                [
                    "2025-02-14",
                    -10.06
                ],
                [
                    "2025-02-17",
                    -10.13
                ],
                [
                    "2025-02-18",
                    6.14
                ],
                [
                    "2025-02-19",
                    5.49
                ],
                [
                    "2025-02-20",
                    -21.06
                ],
                [
                    "2025-02-21",
                    -6.88
                ],
                [
                    "2025-02-24",
                    11.26
                ],
                [
                    "2025-02-25",
                    -0.75
                ],
                [
                    "2025-02-26",
                    4.51
                ],
                [
                    "2025-02-27",
                    12.22
                ],
                [
                    "2025-02-28",
                    -4.24
                ],
                [
                    "2025-03-03",
                    1.52
                ],
                [
                    "2025-03-04",
                    -1.46
                ],
                [
                    "2025-03-05",
                    -3.96
                ],
                [
                    "2025-03-06",
                    -9.4
                ],
                [
                    "2025-03-07",
                    -2.51
                ],
                [
                    "2025-03-10",
                    -6.74
                ],
                [
                    "2025-03-11",
                    -6.19
                ],
                [
                    "2025-03-12",
                    -3.07
                ],
                [
                    "2025-03-13",
                    1.91
                ],
                [
                    "2025-03-14",
                    -1.88
                ],
                [
                    "2025-03-17",
                    -6.74
                ],
                [
                    "2025-03-18",
                    -9.35
                ],
                [
                    "2025-03-19",
                    2.06
                ],
                [
                    "2025-03-20",
                    -9.18
                ],
                [
                    "2025-03-21",
                    -18.0
                ],
                [
                    "2025-03-24",
                    5.47
                ],
                [
                    "2025-03-25",
                    3.09
                ],
                [
                    "2025-03-26",
                    -0.29
                ],
                [
                    "2025-03-27",
                    0.84
                ],
                [
                    "2025-03-28",
                    14.17
                ],
                [
                    "2025-03-31",
                    3.07
                ],
                [
                    "2025-04-01",
                    -9.41
                ],
                [
                    "2025-04-02",
                    -7.92
                ],
                [
                    "2025-04-03",
                    6.68
                ],
                [
                    "2025-04-07",
                    -3.27
                ],
                [
                    "2025-04-08",
                    -10.52
                ],
                [
                    "2025-04-09",
                    -3.8
                ],
                [
                    "2025-04-10",
                    14.97
                ],
                [
                    "2025-04-11",
                    10.94
                ],
                [
                    "2025-04-14",
                    -9.15
                ],
                [
                    "2025-04-15",
                    -0.3
                ],
                [
                    "2025-04-16",
                    5.97
                ],
                [
                    "2025-04-17",
                    -20.32
                ],
                [
                    "2025-04-18",
                    -2.75
                ],
                [
                    "2025-04-21",
                    12.7
                ],
                [
                    "2025-04-22",
                    -8.03
                ],
                [
                    "2025-04-23",
                    -8.57
                ],
                [
                    "2025-04-24",
                    3.24
                ],
                [
                    "2025-04-25",
                    -12.34
                ],
                [
                    "2025-04-28",
                    -1.25
                ],
                [
                    "2025-04-29",
                    5.57
                ],
                [
                    "2025-04-30",
                    -9.56
                ],
                [
                    "2025-05-06",
                    10.21
                ],
                [
                    "2025-05-07",
                    2.12
                ],
                [
                    "2025-05-08",
                    -7.25
                ],
                [
                    "2025-05-09",
                    -6.28
                ],
                [
                    "2025-05-12",
                    -13.34
                ],
                [
                    "2025-05-13",
                    12.64
                ],
                [
                    "2025-05-14",
                    -5.39
                ],
                [
                    "2025-05-15",
                    -6.38
                ],
                [
                    "2025-05-16",
                    4.33
                ],
                [
                    "2025-05-19",
                    -1.78
                ],
                [
                    "2025-05-20",
                    0.21
                ],
                [
                    "2025-05-21",
                    7.07
                ],
                [
                    "2025-05-22",
                    -3.57
                ],
                [
                    "2025-05-23",
                    0.54
                ],
                [
                    "2025-05-26",
                    -9.08
                ],
                [
                    "2025-05-27",
                    -8.71
                ],
                [
                    "2025-05-28",
                    -6.01
                ],
                [
                    "2025-05-29",
                    -6.25
                ],
                [
                    "2025-05-30",
                    3.68
                ],
                [
                    "2025-06-03",
                    5.78
                ],
                [
                    "2025-06-04",
                    -7.52
                ],
                [
                    "2025-06-05",
                    -3.11
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5470C6"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "symbol": "triangle",
            "symbolSize": 12,
            "data": [
                [
                    "2024-12-04",
                    null
                ],
                [
                    "2024-12-05",
                    null
                ],
                [
                    "2024-12-06",
                    null
                ],
                [
                    "2024-12-09",
                    null
                ],
                [
                    "2024-12-10",
                    null
                ],
                [
                    "2024-12-11",
                    null
                ],
                [
                    "2024-12-12",
                    null
                ],
                [
                    "2024-12-13",
                    null
                ],
                [
                    "2024-12-16",
                    null
                ],
                [
                    "2024-12-17",
                    null
                ],
                [
                    "2024-12-18",
                    null
                ],
                [
                    "2024-12-19",
                    null
                ],
                [
                    "2024-12-20",
                    null
                ],
                [
                    "2024-12-23",
                    null
                ],
                [
                    "2024-12-24",
                    null
                ],
                [
                    "2024-12-25",
                    null
                ],
                [
                    "2024-12-26",
                    20.99
                ],
                [
                    "2024-12-27",
                    null
                ],
                [
                    "2024-12-30",
                    null
                ],
                [
                    "2024-12-31",
                    null
                ],
                [
                    "2025-01-02",
                    null
                ],
                [
                    "2025-01-03",
                    null
                ],
                [
                    "2025-01-06",
                    null
                ],
                [
                    "2025-01-07",
                    null
                ],
                [
                    "2025-01-08",
                    null
                ],
                [
                    "2025-01-09",
                    21.8
                ],
                [
                    "2025-01-10",
                    null
                ],
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    null
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    21.67
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    null
                ],
                [
                    "2025-02-26",
                    21.63
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    null
                ],
                [
                    "2025-03-04",
                    null
                ],
                [
                    "2025-03-05",
                    null
                ],
                [
                    "2025-03-06",
                    null
                ],
                [
                    "2025-03-07",
                    null
                ],
                [
                    "2025-03-10",
                    null
                ],
                [
                    "2025-03-11",
                    null
                ],
                [
                    "2025-03-12",
                    null
                ],
                [
                    "2025-03-13",
                    null
                ],
                [
                    "2025-03-14",
                    null
                ],
                [
                    "2025-03-17",
                    null
                ],
                [
                    "2025-03-18",
                    null
                ],
                [
                    "2025-03-19",
                    null
                ],
                [
                    "2025-03-20",
                    null
                ],
                [
                    "2025-03-21",
                    null
                ],
                [
                    "2025-03-24",
                    21.86
                ],
                [
                    "2025-03-25",
                    null
                ],
                [
                    "2025-03-26",
                    null
                ],
                [
                    "2025-03-27",
                    null
                ],
                [
                    "2025-03-28",
                    null
                ],
                [
                    "2025-03-31",
                    null
                ],
                [
                    "2025-04-01",
                    null
                ],
                [
                    "2025-04-02",
                    null
                ],
                [
                    "2025-04-03",
                    null
                ],
                [
                    "2025-04-07",
                    null
                ],
                [
                    "2025-04-08",
                    null
                ],
                [
                    "2025-04-09",
                    null
                ],
                [
                    "2025-04-10",
                    null
                ],
                [
                    "2025-04-11",
                    null
                ],
                [
                    "2025-04-14",
                    null
                ],
                [
                    "2025-04-15",
                    null
                ],
                [
                    "2025-04-16",
                    null
                ],
                [
                    "2025-04-17",
                    null
                ],
                [
                    "2025-04-18",
                    null
                ],
                [
                    "2025-04-21",
                    null
                ],
                [
                    "2025-04-22",
                    null
                ],
                [
                    "2025-04-23",
                    null
                ],
                [
                    "2025-04-24",
                    null
                ],
                [
                    "2025-04-25",
                    null
                ],
                [
                    "2025-04-28",
                    null
                ],
                [
                    "2025-04-29",
                    null
                ],
                [
                    "2025-04-30",
                    null
                ],
                [
                    "2025-05-06",
                    null
                ],
                [
                    "2025-05-07",
                    null
                ],
                [
                    "2025-05-08",
                    null
                ],
                [
                    "2025-05-09",
                    null
                ],
                [
                    "2025-05-12",
                    null
                ],
                [
                    "2025-05-13",
                    null
                ],
                [
                    "2025-05-14",
                    null
                ],
                [
                    "2025-05-15",
                    null
                ],
                [
                    "2025-05-16",
                    null
                ],
                [
                    "2025-05-19",
                    null
                ],
                [
                    "2025-05-20",
                    null
                ],
                [
                    "2025-05-21",
                    null
                ],
                [
                    "2025-05-22",
                    null
                ],
                [
                    "2025-05-23",
                    null
                ],
                [
                    "2025-05-26",
                    null
                ],
                [
                    "2025-05-27",
                    null
                ],
                [
                    "2025-05-28",
                    null
                ],
                [
                    "2025-05-29",
                    null
                ],
                [
                    "2025-05-30",
                    23.12
                ],
                [
                    "2025-06-03",
                    null
                ],
                [
                    "2025-06-04",
                    null
                ],
                [
                    "2025-06-05",
                    null
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "purple"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "001337 \u4e3b\u529b\u5165\u573a\u4fe1\u53f7 K \u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "50%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_14b97e25757a4c72a3fa5bd278622c2e.setOption(option_14b97e25757a4c72a3fa5bd278622c2e);
    </script>
</body>
</html>
