<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="93f504a04a794901b8f13a0160d038c3" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_93f504a04a794901b8f13a0160d038c3 = echarts.init(
            document.getElementById('93f504a04a794901b8f13a0160d038c3'), 'white', {renderer: 'canvas'});
        var option_93f504a04a794901b8f13a0160d038c3 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K Line",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    8.84,
                    8.77,
                    8.75,
                    8.93
                ],
                [
                    8.77,
                    8.76,
                    8.69,
                    8.79
                ],
                [
                    8.76,
                    8.86,
                    8.73,
                    8.88
                ],
                [
                    8.85,
                    8.83,
                    8.78,
                    8.92
                ],
                [
                    9.04,
                    9.03,
                    8.97,
                    9.24
                ],
                [
                    9.08,
                    9.47,
                    9.03,
                    9.64
                ],
                [
                    9.35,
                    9.96,
                    9.24,
                    9.96
                ],
                [
                    9.81,
                    9.47,
                    9.45,
                    9.82
                ],
                [
                    9.48,
                    9.26,
                    9.2,
                    9.56
                ],
                [
                    9.24,
                    9.07,
                    9.03,
                    9.26
                ],
                [
                    9.08,
                    8.99,
                    8.96,
                    9.12
                ],
                [
                    8.94,
                    8.92,
                    8.81,
                    8.95
                ],
                [
                    8.93,
                    8.95,
                    8.89,
                    9.09
                ],
                [
                    8.92,
                    8.74,
                    8.71,
                    8.96
                ],
                [
                    8.71,
                    8.83,
                    8.71,
                    8.84
                ],
                [
                    8.83,
                    8.77,
                    8.65,
                    8.83
                ],
                [
                    8.73,
                    8.74,
                    8.72,
                    8.83
                ],
                [
                    8.75,
                    8.8,
                    8.68,
                    8.84
                ],
                [
                    8.8,
                    8.64,
                    8.58,
                    8.81
                ],
                [
                    8.68,
                    8.54,
                    8.54,
                    8.73
                ],
                [
                    8.54,
                    8.43,
                    8.36,
                    8.71
                ],
                [
                    8.45,
                    8.29,
                    8.25,
                    8.49
                ],
                [
                    8.25,
                    8.23,
                    8.12,
                    8.34
                ],
                [
                    8.22,
                    8.26,
                    8.14,
                    8.28
                ],
                [
                    8.23,
                    8.21,
                    8.09,
                    8.26
                ],
                [
                    8.18,
                    8.13,
                    8.1,
                    8.21
                ],
                [
                    8.13,
                    7.99,
                    7.98,
                    8.17
                ],
                [
                    7.96,
                    8.0,
                    7.89,
                    8.03
                ],
                [
                    8.0,
                    8.16,
                    8.0,
                    8.17
                ],
                [
                    8.13,
                    8.1,
                    8.08,
                    8.18
                ],
                [
                    8.12,
                    8.1,
                    8.06,
                    8.2
                ],
                [
                    8.08,
                    8.13,
                    8.03,
                    8.17
                ],
                [
                    8.16,
                    8.17,
                    8.13,
                    8.22
                ],
                [
                    8.2,
                    8.17,
                    8.1,
                    8.21
                ],
                [
                    8.12,
                    8.07,
                    8.04,
                    8.15
                ],
                [
                    8.13,
                    8.07,
                    8.07,
                    8.2
                ],
                [
                    8.07,
                    8.1,
                    8.05,
                    8.13
                ],
                [
                    8.14,
                    8.15,
                    8.12,
                    8.21
                ],
                [
                    8.17,
                    8.06,
                    8.04,
                    8.19
                ],
                [
                    8.06,
                    8.12,
                    8.02,
                    8.12
                ],
                [
                    8.1,
                    8.19,
                    8.08,
                    8.23
                ],
                [
                    8.19,
                    8.25,
                    8.16,
                    8.27
                ],
                [
                    8.27,
                    8.21,
                    8.16,
                    8.29
                ],
                [
                    8.2,
                    8.22,
                    8.15,
                    8.23
                ],
                [
                    8.22,
                    8.24,
                    8.2,
                    8.28
                ],
                [
                    8.25,
                    8.22,
                    8.18,
                    8.26
                ],
                [
                    8.22,
                    8.24,
                    8.16,
                    8.25
                ],
                [
                    8.24,
                    8.12,
                    8.11,
                    8.24
                ],
                [
                    8.13,
                    8.18,
                    8.11,
                    8.18
                ],
                [
                    8.16,
                    8.21,
                    8.16,
                    8.26
                ],
                [
                    8.21,
                    8.2,
                    8.17,
                    8.27
                ],
                [
                    8.21,
                    8.24,
                    8.21,
                    8.29
                ],
                [
                    8.21,
                    8.15,
                    8.13,
                    8.22
                ],
                [
                    8.16,
                    8.21,
                    8.13,
                    8.21
                ],
                [
                    8.22,
                    8.4,
                    8.21,
                    8.42
                ],
                [
                    8.36,
                    8.29,
                    8.28,
                    8.45
                ],
                [
                    8.34,
                    8.21,
                    8.2,
                    8.36
                ],
                [
                    8.18,
                    8.19,
                    8.12,
                    8.2
                ],
                [
                    8.21,
                    8.17,
                    8.13,
                    8.22
                ],
                [
                    8.19,
                    8.21,
                    8.15,
                    8.22
                ],
                [
                    8.19,
                    8.19,
                    8.16,
                    8.23
                ],
                [
                    8.18,
                    8.19,
                    8.15,
                    8.2
                ],
                [
                    8.14,
                    8.26,
                    8.11,
                    8.3
                ],
                [
                    8.26,
                    8.24,
                    8.21,
                    8.3
                ],
                [
                    8.25,
                    8.24,
                    8.19,
                    8.26
                ],
                [
                    8.32,
                    9.06,
                    8.32,
                    9.06
                ],
                [
                    9.2,
                    8.91,
                    8.86,
                    9.23
                ],
                [
                    8.9,
                    8.78,
                    8.73,
                    8.96
                ],
                [
                    8.76,
                    8.65,
                    8.62,
                    8.77
                ],
                [
                    8.68,
                    8.59,
                    8.55,
                    8.82
                ],
                [
                    8.55,
                    8.5,
                    8.47,
                    8.62
                ],
                [
                    8.55,
                    8.63,
                    8.43,
                    8.63
                ],
                [
                    8.63,
                    8.69,
                    8.48,
                    8.75
                ],
                [
                    8.62,
                    8.58,
                    8.56,
                    8.65
                ],
                [
                    8.56,
                    8.52,
                    8.52,
                    8.65
                ],
                [
                    8.52,
                    8.45,
                    8.43,
                    8.53
                ],
                [
                    8.41,
                    8.33,
                    8.3,
                    8.45
                ],
                [
                    8.35,
                    8.39,
                    8.33,
                    8.42
                ],
                [
                    8.41,
                    8.36,
                    8.33,
                    8.42
                ],
                [
                    8.31,
                    8.44,
                    8.3,
                    8.46
                ],
                [
                    8.15,
                    7.86,
                    7.66,
                    8.34
                ],
                [
                    7.86,
                    8.37,
                    7.86,
                    8.38
                ],
                [
                    8.29,
                    8.47,
                    8.09,
                    8.51
                ],
                [
                    8.41,
                    8.71,
                    8.31,
                    8.85
                ],
                [
                    8.61,
                    8.54,
                    8.48,
                    8.68
                ],
                [
                    8.49,
                    8.63,
                    8.46,
                    8.74
                ],
                [
                    8.56,
                    8.58,
                    8.53,
                    8.77
                ],
                [
                    8.55,
                    8.63,
                    8.43,
                    8.68
                ],
                [
                    8.55,
                    8.69,
                    8.51,
                    8.72
                ],
                [
                    8.66,
                    8.54,
                    8.51,
                    8.67
                ],
                [
                    8.49,
                    8.53,
                    8.48,
                    8.63
                ],
                [
                    8.51,
                    8.6,
                    8.49,
                    8.68
                ],
                [
                    8.6,
                    8.45,
                    8.44,
                    8.66
                ],
                [
                    8.44,
                    8.44,
                    8.37,
                    8.48
                ],
                [
                    8.4,
                    8.4,
                    8.39,
                    8.5
                ],
                [
                    8.36,
                    8.21,
                    8.2,
                    8.38
                ],
                [
                    8.19,
                    8.32,
                    8.18,
                    8.32
                ],
                [
                    8.44,
                    8.26,
                    8.26,
                    8.46
                ],
                [
                    8.3,
                    8.36,
                    8.29,
                    8.38
                ],
                [
                    8.4,
                    8.41,
                    8.38,
                    8.48
                ],
                [
                    8.4,
                    8.42,
                    8.33,
                    8.43
                ],
                [
                    8.42,
                    8.42,
                    8.38,
                    8.47
                ],
                [
                    8.44,
                    8.51,
                    8.39,
                    8.51
                ],
                [
                    8.54,
                    8.43,
                    8.39,
                    8.54
                ],
                [
                    8.4,
                    8.46,
                    8.37,
                    8.47
                ],
                [
                    8.44,
                    8.49,
                    8.41,
                    8.5
                ],
                [
                    8.48,
                    8.39,
                    8.38,
                    8.49
                ],
                [
                    8.38,
                    8.5,
                    8.37,
                    8.5
                ],
                [
                    8.49,
                    8.59,
                    8.48,
                    8.65
                ],
                [
                    8.56,
                    8.57,
                    8.54,
                    8.64
                ],
                [
                    8.59,
                    8.45,
                    8.43,
                    8.61
                ],
                [
                    8.47,
                    8.33,
                    8.33,
                    8.47
                ],
                [
                    8.37,
                    8.41,
                    8.3,
                    8.44
                ],
                [
                    8.42,
                    8.56,
                    8.42,
                    8.6
                ],
                [
                    8.54,
                    8.72,
                    8.51,
                    8.77
                ],
                [
                    8.65,
                    8.66,
                    8.62,
                    8.72
                ],
                [
                    8.65,
                    8.68,
                    8.65,
                    8.82
                ],
                [
                    8.67,
                    8.75,
                    8.63,
                    8.78
                ],
                [
                    8.73,
                    8.8,
                    8.66,
                    8.8
                ],
                [
                    8.76,
                    8.63,
                    8.61,
                    8.79
                ]
            ],
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "Bottom Signal",
            "symbol": "circle",
            "symbolSize": 10,
            "data": [
                [
                    "2025-03-05",
                    7.97
                ],
                [
                    "2025-03-31",
                    8.13
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "green"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K Line",
                "Bottom Signal"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600597 K \u7ebf\u56fe\u4e0e\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_93f504a04a794901b8f13a0160d038c3.setOption(option_93f504a04a794901b8f13a0160d038c3);
    </script>
</body>
</html>
