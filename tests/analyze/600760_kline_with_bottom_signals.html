<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="b2f20a232ff5481bb96b0bded63c393d" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_b2f20a232ff5481bb96b0bded63c393d = echarts.init(
            document.getElementById('b2f20a232ff5481bb96b0bded63c393d'), 'white', {renderer: 'canvas'});
        var option_b2f20a232ff5481bb96b0bded63c393d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K Line",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    49.7,
                    48.55,
                    48.09,
                    49.76
                ],
                [
                    48.79,
                    47.71,
                    47.6,
                    48.79
                ],
                [
                    47.72,
                    48.14,
                    47.48,
                    48.48
                ],
                [
                    48.2,
                    48.47,
                    47.5,
                    48.67
                ],
                [
                    48.27,
                    47.65,
                    47.08,
                    48.28
                ],
                [
                    48.69,
                    48.14,
                    48.0,
                    49.36
                ],
                [
                    47.98,
                    48.49,
                    47.76,
                    48.7
                ],
                [
                    48.99,
                    50.51,
                    48.99,
                    50.72
                ],
                [
                    49.98,
                    48.59,
                    48.51,
                    50.05
                ],
                [
                    48.6,
                    49.44,
                    48.35,
                    49.75
                ],
                [
                    49.57,
                    49.35,
                    49.2,
                    49.9
                ],
                [
                    49.4,
                    50.0,
                    49.4,
                    50.69
                ],
                [
                    49.5,
                    49.64,
                    48.85,
                    49.85
                ],
                [
                    49.48,
                    50.09,
                    49.33,
                    50.28
                ],
                [
                    49.96,
                    48.49,
                    48.31,
                    49.96
                ],
                [
                    48.25,
                    48.58,
                    48.12,
                    48.69
                ],
                [
                    48.56,
                    48.17,
                    47.78,
                    48.99
                ],
                [
                    49.0,
                    49.6,
                    49.0,
                    50.67
                ],
                [
                    52.01,
                    50.46,
                    50.22,
                    52.5
                ],
                [
                    50.59,
                    50.1,
                    49.6,
                    51.4
                ],
                [
                    49.9,
                    50.72,
                    49.6,
                    51.31
                ],
                [
                    50.17,
                    48.32,
                    47.88,
                    50.17
                ],
                [
                    48.03,
                    46.34,
                    46.21,
                    48.33
                ],
                [
                    46.34,
                    45.75,
                    44.91,
                    46.67
                ],
                [
                    45.64,
                    46.4,
                    45.51,
                    46.5
                ],
                [
                    46.2,
                    45.06,
                    43.6,
                    46.25
                ],
                [
                    44.69,
                    47.49,
                    44.51,
                    49.49
                ],
                [
                    46.85,
                    47.15,
                    46.85,
                    48.48
                ],
                [
                    46.5,
                    46.93,
                    46.07,
                    47.86
                ],
                [
                    46.95,
                    48.28,
                    46.88,
                    48.38
                ],
                [
                    48.29,
                    47.5,
                    47.5,
                    48.43
                ],
                [
                    47.59,
                    47.16,
                    46.99,
                    47.99
                ],
                [
                    47.0,
                    47.98,
                    46.9,
                    48.19
                ],
                [
                    48.0,
                    47.71,
                    47.4,
                    48.09
                ],
                [
                    47.76,
                    47.0,
                    46.77,
                    47.87
                ],
                [
                    46.9,
                    46.9,
                    45.59,
                    47.0
                ],
                [
                    47.26,
                    47.17,
                    46.8,
                    47.45
                ],
                [
                    46.83,
                    46.93,
                    46.68,
                    47.24
                ],
                [
                    47.2,
                    46.56,
                    46.35,
                    47.2
                ],
                [
                    46.8,
                    46.59,
                    45.8,
                    46.97
                ],
                [
                    46.55,
                    47.95,
                    46.12,
                    48.48
                ],
                [
                    47.65,
                    47.94,
                    47.44,
                    48.35
                ],
                [
                    47.94,
                    48.18,
                    47.6,
                    48.28
                ],
                [
                    48.01,
                    47.2,
                    47.11,
                    48.01
                ],
                [
                    47.03,
                    47.22,
                    46.57,
                    47.27
                ],
                [
                    47.22,
                    47.22,
                    47.06,
                    48.02
                ],
                [
                    47.0,
                    46.31,
                    46.14,
                    47.2
                ],
                [
                    46.32,
                    45.4,
                    45.1,
                    46.5
                ],
                [
                    45.4,
                    44.62,
                    44.25,
                    45.46
                ],
                [
                    44.61,
                    44.92,
                    44.39,
                    44.98
                ],
                [
                    44.88,
                    45.41,
                    44.43,
                    45.69
                ],
                [
                    45.3,
                    45.9,
                    45.0,
                    46.23
                ],
                [
                    45.86,
                    45.36,
                    45.13,
                    45.98
                ],
                [
                    45.08,
                    44.7,
                    44.25,
                    45.28
                ],
                [
                    44.44,
                    44.25,
                    43.93,
                    44.67
                ],
                [
                    44.3,
                    44.05,
                    43.51,
                    44.39
                ],
                [
                    44.05,
                    43.54,
                    43.49,
                    44.36
                ],
                [
                    43.58,
                    43.58,
                    43.33,
                    44.07
                ],
                [
                    43.8,
                    44.88,
                    43.6,
                    45.1
                ],
                [
                    44.68,
                    44.29,
                    44.11,
                    45.09
                ],
                [
                    44.48,
                    44.36,
                    44.0,
                    44.54
                ],
                [
                    44.25,
                    45.32,
                    44.01,
                    46.32
                ],
                [
                    45.59,
                    45.17,
                    45.01,
                    46.29
                ],
                [
                    44.88,
                    46.31,
                    44.69,
                    46.61
                ],
                [
                    46.31,
                    45.45,
                    45.39,
                    46.98
                ],
                [
                    45.48,
                    45.08,
                    44.77,
                    45.66
                ],
                [
                    45.08,
                    45.17,
                    44.59,
                    45.44
                ],
                [
                    45.17,
                    45.24,
                    44.97,
                    46.07
                ],
                [
                    45.26,
                    44.75,
                    44.7,
                    45.35
                ],
                [
                    44.76,
                    44.19,
                    44.08,
                    44.79
                ],
                [
                    44.1,
                    44.45,
                    44.08,
                    45.1
                ],
                [
                    44.02,
                    43.97,
                    43.68,
                    44.49
                ],
                [
                    44.03,
                    43.85,
                    43.35,
                    44.25
                ],
                [
                    43.83,
                    43.77,
                    43.6,
                    44.18
                ],
                [
                    43.78,
                    43.93,
                    43.56,
                    44.02
                ],
                [
                    43.93,
                    43.6,
                    43.34,
                    43.99
                ],
                [
                    43.6,
                    43.42,
                    43.03,
                    43.67
                ],
                [
                    42.91,
                    42.28,
                    42.26,
                    43.13
                ],
                [
                    43.68,
                    44.49,
                    43.68,
                    45.13
                ],
                [
                    44.49,
                    42.9,
                    42.73,
                    44.49
                ],
                [
                    42.68,
                    42.94,
                    42.53,
                    43.16
                ],
                [
                    41.68,
                    41.24,
                    39.06,
                    42.63
                ],
                [
                    41.22,
                    42.0,
                    41.13,
                    42.0
                ],
                [
                    41.91,
                    44.27,
                    41.66,
                    44.81
                ],
                [
                    44.26,
                    44.6,
                    43.57,
                    45.1
                ],
                [
                    44.6,
                    44.83,
                    44.03,
                    44.83
                ],
                [
                    44.83,
                    44.73,
                    44.16,
                    44.95
                ],
                [
                    44.5,
                    44.0,
                    43.8,
                    44.51
                ],
                [
                    44.7,
                    44.48,
                    43.58,
                    44.75
                ],
                [
                    44.1,
                    44.2,
                    43.96,
                    44.6
                ],
                [
                    43.93,
                    44.04,
                    43.31,
                    44.18
                ],
                [
                    43.83,
                    44.2,
                    43.74,
                    44.4
                ],
                [
                    44.0,
                    43.88,
                    43.73,
                    44.22
                ],
                [
                    43.88,
                    43.05,
                    43.01,
                    43.97
                ],
                [
                    42.99,
                    42.68,
                    42.67,
                    43.07
                ],
                [
                    42.71,
                    42.84,
                    42.31,
                    42.93
                ],
                [
                    42.8,
                    42.48,
                    42.4,
                    42.83
                ],
                [
                    41.5,
                    42.67,
                    41.28,
                    43.0
                ],
                [
                    42.43,
                    42.66,
                    42.4,
                    42.78
                ],
                [
                    42.98,
                    43.22,
                    42.9,
                    43.5
                ],
                [
                    44.4,
                    46.01,
                    44.35,
                    47.47
                ],
                [
                    46.15,
                    48.66,
                    45.13,
                    49.66
                ],
                [
                    48.49,
                    46.18,
                    46.03,
                    48.5
                ],
                [
                    46.58,
                    50.07,
                    46.25,
                    50.7
                ],
                [
                    49.5,
                    47.81,
                    47.54,
                    49.5
                ],
                [
                    47.82,
                    47.38,
                    46.89,
                    48.19
                ],
                [
                    47.09,
                    46.92,
                    46.69,
                    47.75
                ],
                [
                    46.93,
                    47.2,
                    46.83,
                    48.05
                ],
                [
                    47.76,
                    47.69,
                    47.47,
                    48.96
                ],
                [
                    47.29,
                    47.68,
                    46.4,
                    48.2
                ],
                [
                    47.16,
                    47.99,
                    46.89,
                    48.29
                ],
                [
                    48.0,
                    48.13,
                    47.74,
                    49.86
                ],
                [
                    47.8,
                    48.01,
                    47.8,
                    48.85
                ],
                [
                    48.21,
                    48.33,
                    47.55,
                    48.94
                ],
                [
                    48.03,
                    47.65,
                    47.11,
                    48.59
                ],
                [
                    47.51,
                    47.25,
                    47.17,
                    48.18
                ],
                [
                    47.13,
                    47.58,
                    46.93,
                    47.83
                ],
                [
                    47.35,
                    48.03,
                    46.95,
                    48.66
                ],
                [
                    48.5,
                    49.38,
                    48.25,
                    50.0
                ],
                [
                    49.4,
                    49.36,
                    48.8,
                    49.43
                ],
                [
                    49.38,
                    49.6,
                    48.7,
                    49.93
                ]
            ],
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "Bottom Signal",
            "symbol": "circle",
            "symbolSize": 10,
            "data": [
                [
                    "2025-03-21",
                    42.81
                ],
                [
                    "2025-03-24",
                    42.48
                ],
                [
                    "2025-03-25",
                    42.73
                ],
                [
                    "2025-03-26",
                    42.69
                ],
                [
                    "2025-03-27",
                    42.47
                ],
                [
                    "2025-03-28",
                    42.17
                ],
                [
                    "2025-03-31",
                    41.41
                ],
                [
                    "2025-04-02",
                    41.88
                ],
                [
                    "2025-04-03",
                    41.68
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "green"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K Line",
                "Bottom Signal"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600760 K \u7ebf\u56fe\u4e0e\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_b2f20a232ff5481bb96b0bded63c393d.setOption(option_b2f20a232ff5481bb96b0bded63c393d);
    </script>
</body>
</html>
