import pandas as pd
import numpy as np
from pyecharts.charts import Line, Scatter, Grid
from pyecharts import options as opts

from pyecharts.charts import Kline, Line, Scatter, Grid
from pyecharts import options as opts

code = "600760"
daily_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
fund_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"
daily_data = pd.read_csv(daily_path)
# 读取资金流数据
funds_data = pd.read_csv(fund_path)

# === 0. 配置及文件路径 ===
# 你本地的日线 CSV 文件（注意：列名需含“日期, 开盘, 最高, 最低, 收盘”）
daily_path = "daily_data.csv"
# 你本地的资金流 CSV 文件（需含“日期, 超大单净流入-净占比, 大单净流入-净占比, 中单净流入-净占比, 小单净流入-净占比”）
fund_path = "fund_flow_data.csv"

# 滚动窗口长度，用作背离判断
window = 3


# === 1. 读取并预处理日线数据 ===
# daily_raw = pd.read_csv(daily_path, encoding="utf-8")

# 重命名为统一的英文列名（假设你的 CSV 用“日期, 开盘价, 最高价, 最低价, 收盘价”五列）
# 如果你的列名是“日期, 开盘, 最高, 最低, 收盘”，请相应改下面的映射
daily = daily_data.rename(columns={
    "trade_date": "date",
    "open": "open",
    "high": "high",
    "low": "low",
    "close": "close",
    "vol": "vol"
})[["date", "open", "high", "low", "close", "vol"]]

# 转换日期格式，并设为索引后按日期排序
daily["date"] = pd.to_datetime(daily["date"], format="%Y%m%d")
daily = daily.set_index("date").sort_index()

# 准备 K 线图所需的数据：日期列表 + [(开盘, 收盘, 最低, 最高), ...] 列表
kline_dates  = [d.strftime("%Y-%m-%d") for d in daily.index]
kline_values = daily[["open", "close", "low", "high"]].round(2).values.tolist()


# === 2. 读取并预处理资金流数据 ===
# funds_raw = pd.read_csv(fund_path, encoding="utf-8")

# 假设你的资金流 CSV 中列名为：
# “日期, 超大单净流入-净占比, 大单净流入-净占比, 中单净流入-净占比, 小单净流入-净占比, ...”
funds = funds_data.rename(columns={
    "日期": "date",
    "超大单净流入-净占比": "elg_net_pct",
    "大单净流入-净占比": "large_net_pct",
    "中单净流入-净占比": "medium_net_pct",
    "小单净流入-净占比": "small_net_pct"
})[["date", "elg_net_pct", "large_net_pct", "medium_net_pct", "small_net_pct"]]



funds["date"] = pd.to_datetime(funds["date"], format="%Y-%m-%d")
funds = funds.set_index("date").sort_index()

# 计算三条净占比：主力（大单+超大单）、游资（中单）、散户（小单）
funds["main_net_pct"]   = funds["elg_net_pct"] + funds["large_net_pct"]
funds["heat_net_pct"]   = funds["medium_net_pct"]
funds["retail_net_pct"] = funds["small_net_pct"]

# 计算滚动均值，用于背离判断
funds["main_trend"]   = funds["main_net_pct"].rolling(window).mean()
funds["retail_trend"] = funds["retail_net_pct"].rolling(window).mean()
funds["heat_trend"]   = funds["heat_net_pct"].rolling(window).mean()

# 找出各种背离日期
mask_main   = (funds["main_trend"] > 0)   & (funds["retail_trend"] < 0)
mask_heat   = (funds["heat_trend"] > 0)   & (funds["retail_trend"] < 0)
mask_retail = (funds["retail_trend"] > 0) & (funds["main_trend"] < 0)

div_main_dates   = funds[mask_main].index.tolist()
div_heat_dates   = funds[mask_heat].index.tolist()
div_retail_dates = funds[mask_retail].index.tolist()

# 转成字符串
all_flow_dates   = [d.strftime("%Y-%m-%d") for d in funds.index]
div_main_str     = [d.strftime("%Y-%m-%d") for d in div_main_dates]
div_heat_str     = [d.strftime("%Y-%m-%d") for d in div_heat_dates]
div_retail_str   = [d.strftime("%Y-%m-%d") for d in div_retail_dates]

# 对应背离点的净占比数值
div_main_vals   = funds.loc[div_main_dates,   "main_net_pct"].round(2).tolist()
div_heat_vals   = funds.loc[div_heat_dates,   "heat_net_pct"].round(2).tolist()
div_retail_vals = funds.loc[div_retail_dates, "retail_net_pct"].round(2).tolist()

# 三条折线的完整数据
main_vals   = funds["main_net_pct"].round(2).tolist()
heat_vals   = funds["heat_net_pct"].round(2).tolist()
retail_vals = funds["retail_net_pct"].round(2).tolist()

# === 3. 绘制 K 线图 ===
kline = (
    Kline(init_opts=opts.InitOpts(width="100%", height="400px"))
    .add_xaxis(kline_dates)
    .add_yaxis(
        series_name="K 线",
        y_axis=kline_values,
        itemstyle_opts=opts.ItemStyleOpts(color="#ec0000", color0="#00da3c")
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="日线 K 线图（联动资金流）"),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="cross",
            is_show=True
        ),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            is_scale=True,
            boundary_gap=False,
            axisline_opts=opts.AxisLineOpts(is_on_zero=False),
            splitline_opts=opts.SplitLineOpts(is_show=False),
            axislabel_opts=opts.LabelOpts(is_show=False),
            # 关键：设置轴指针联动
            axispointer_opts=opts.AxisPointerOpts(
                is_show=True,
                link=[{"xAxisIndex": "all"}]
            )
        ),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            splitline_opts=opts.SplitLineOpts(is_show=True)
        ),
        datazoom_opts=[
            opts.DataZoomOpts(type_="inside", xaxis_index=[0, 1], range_start=0, range_end=100),
            opts.DataZoomOpts(is_show=True, xaxis_index=[0, 1], range_start=0, range_end=100),
        ],
        # 关键：设置画刷联动
        brush_opts=opts.BrushOpts(x_axis_index="all")
    )
)

# === 4. 绘制资金流折线图，并叠加背离散点 ===
flow_line = (
    Line(init_opts=opts.InitOpts(width="100%", height="300px"))
    .add_xaxis(all_flow_dates)
    .add_yaxis(
        series_name="主力净占比",
        y_axis=main_vals,
        is_smooth=True,
        linestyle_opts=opts.LineStyleOpts(color="#1f77b4", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )
    .add_yaxis(
        series_name="游资净占比",
        y_axis=heat_vals,
        is_smooth=True,
        linestyle_opts=opts.LineStyleOpts(color="#9467bd", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )
    .add_yaxis(
        series_name="散户净占比",
        y_axis=retail_vals,
        is_smooth=True,
        linestyle_opts=opts.LineStyleOpts(color="#ff7f0e", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )

    .set_global_opts(
        title_opts=opts.TitleOpts(title="资金流对比与背离标注"),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="cross",
            is_show=True
        ),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            boundary_gap=False,
            axislabel_opts=opts.LabelOpts(rotate=-45),
            # 关键：设置轴指针联动
            axispointer_opts=opts.AxisPointerOpts(
                is_show=True,
                link=[{"xAxisIndex": "all"}]
            )
        ),
        yaxis_opts=opts.AxisOpts(name="净占比 (%)", is_scale=True),
        datazoom_opts=[opts.DataZoomOpts(type_="slider", xaxis_index=[0, 1], range_start=0, range_end=100)],
        legend_opts=opts.LegendOpts(pos_top="5%"),
        # 关键：设置画刷联动
        brush_opts=opts.BrushOpts(x_axis_index="all")
    )
)

# 创建背离点散点图
scatter_main = (
    Scatter()
    .add_xaxis(div_main_str)
    .add_yaxis(
        series_name="主力背离",
        y_axis=div_main_vals,
        symbol="triangle",
        symbol_size=15,
        itemstyle_opts=opts.ItemStyleOpts(color="red")
    )
)

scatter_heat = (
    Scatter()
    .add_xaxis(div_heat_str)
    .add_yaxis(
        series_name="游资背离",
        y_axis=div_heat_vals,
        symbol="diamond",
        symbol_size=15,
        itemstyle_opts=opts.ItemStyleOpts(color="gold")
    )
)

scatter_retail = (
    Scatter()
    .add_xaxis(div_retail_str)
    .add_yaxis(
        series_name="散户背离",
        y_axis=div_retail_vals,
        symbol="circle",
        symbol_size=15,
        itemstyle_opts=opts.ItemStyleOpts(color="black")
    )
)

# 将散点图叠加到折线图上
flow_line = flow_line.overlap(scatter_main).overlap(scatter_heat).overlap(scatter_retail)

# === 5. 用 Grid 将两张图合并并实现联动 ===
grid = (
    Grid(init_opts=opts.InitOpts(width="100%", height="750px"))
    .add(kline, grid_opts=opts.GridOpts(pos_left="5%", pos_right="5%", pos_top="5%", height="45%"))
    .add(flow_line, grid_opts=opts.GridOpts(pos_left="5%", pos_right="5%", pos_top="55%", height="40%"))
)

# 添加联动的JavaScript代码
grid.add_js_funcs("""
    // 等待页面加载完成
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        // 设置缩放联动
        if (charts.length >= 2) {
            echarts.connect(charts);
            console.log('图表缩放联动已设置，共' + charts.length + '个图表');

            // 设置鼠标悬停联动
            charts.forEach(function(chart, index) {
                chart.on('mousemove', function(params) {
                    // 获取当前鼠标位置对应的数据索引
                    var dataIndex = params.dataIndex;
                    if (dataIndex !== undefined && dataIndex !== null) {
                        // 在其他图表上显示相同索引的tooltip
                        charts.forEach(function(otherChart, otherIndex) {
                            if (otherIndex !== index) {
                                otherChart.dispatchAction({
                                    type: 'showTip',
                                    seriesIndex: 0,
                                    dataIndex: dataIndex
                                });
                            }
                        });
                    }
                });

                // 鼠标离开时隐藏其他图表的tooltip
                chart.on('mouseleave', function() {
                    charts.forEach(function(otherChart, otherIndex) {
                        if (otherIndex !== index) {
                            otherChart.dispatchAction({
                                type: 'hideTip'
                            });
                        }
                    });
                });

                // 监听tooltip显示事件
                chart.on('highlight', function(params) {
                    var dataIndex = params.dataIndex;
                    if (dataIndex !== undefined && dataIndex !== null) {
                        charts.forEach(function(otherChart, otherIndex) {
                            if (otherIndex !== index) {
                                otherChart.dispatchAction({
                                    type: 'highlight',
                                    seriesIndex: 0,
                                    dataIndex: dataIndex
                                });
                            }
                        });
                    }
                });

                // 监听tooltip隐藏事件
                chart.on('downplay', function(params) {
                    var dataIndex = params.dataIndex;
                    if (dataIndex !== undefined && dataIndex !== null) {
                        charts.forEach(function(otherChart, otherIndex) {
                            if (otherIndex !== index) {
                                otherChart.dispatchAction({
                                    type: 'downplay',
                                    seriesIndex: 0,
                                    dataIndex: dataIndex
                                });
                            }
                        });
                    }
                });
            });

            console.log('鼠标悬停联动已设置');
        }
    }, 500);
""")

# 4. 渲染成 HTML，会自动自适应宽度
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_fund_flow_divergence.html"
grid.render(html_path)
print(f"图表已生成: {html_path}")
print("联动功能说明：")
print("1. 鼠标悬停在任一图表上，两个图表会同时显示相同时间点的数据")
print("2. 拖拽缩放任一图表，另一个图表会同步缩放")
print("3. 数据缩放滑块操作会同时影响两个图表")

